"""配置对话框组件。

负责服务端配置的编辑和保存。
"""
from tkinter import messagebox

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import ttkb_gui_utils, gui_utils
from webhook_server.config import gui_constants, constants
from webhook_server.models import gui_properties


class ConfigDialogComponent:
    """配置对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化关于对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props

    def open_config_dialog(self):
        """打开配置对话框。"""
        if getattr(self.props, "server_config_dialog", None) and self.props.server_config_dialog.winfo_exists():
            self.props.server_config_dialog.lift()
            return

        self.props.server_config_dialog = ttkb.Toplevel(master=self.props.root,title="服务端配置",size=gui_constants.SERVER_CONFIG_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.props.root)
        ttkb_gui_utils.comm_child_win_do(self.props.server_config_dialog, self.props.root)

        server_config_frame = ttkb.Frame(self.props.server_config_dialog)
        server_config_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 使用 grid 布局统一控制整体布局
        config_frame = ttkb.LabelFrame(server_config_frame, text="服务端配置")
        config_frame.grid(row=0, column=0, sticky=NSEW, padx=10, pady=(0, 10))

        self.props.config_input_widgets.clear()

        for row, config_key in enumerate(gui_constants.USER_CUSTOM_KEYS):
            ttkb.Label(config_frame, text=f"{constants.SERVER_KEY_DESC[config_key]}:").grid(row=row, column=0, sticky=E, padx=5, pady=5)

            if config_key == "run_time":
                # run_time配置项使用特殊的布局
                self._set_run_time_entry(config_frame,row)
            else:
                # 其他配置项使用普通Entry 输入框
                server_custom_config_entry = ttkb.Entry(config_frame, width=25)
                server_custom_config_entry.grid(row=row, column=1, sticky=W, padx=5, pady=5)

                # 如果配置中已有值则显示
                existing_value = self.props.server_config_values.get(config_key)
                if existing_value:
                    server_custom_config_entry.insert(0, existing_value)

                # 添加对应的配置项的校验器
                if config_key == "api_key":
                    ttkb_gui_utils.comm_entry_validate(server_custom_config_entry, validate_func=constants.SERVER_API_KEY_PATTERN.fullmatch)

                self.props.config_input_widgets[config_key] = server_custom_config_entry

        # 保存按钮
        save_frame = ttkb.Frame(server_config_frame)
        save_frame.grid(row=1, column=0, sticky=E, pady=(10, 0))

        ttkb.Button(save_frame, text="保存服务端配置", command=self.save_config_changes).pack(anchor=E, padx=10)

        # 关闭事件
        self.props.server_config_dialog.protocol("WM_DELETE_WINDOW", self.props.server_config_dialog.destroy)

    def _set_run_time_entry(self,config_frame:ttkb.LabelFrame,cur_row:int):
        """设置run_time的Entry控件"""
        cur_run_time='run_time'
        # 为run_time创建特殊的Spinbox控件布局
        spinbox_width = 2
        time_frame = ttkb.Frame(config_frame)
        time_frame.grid(row=cur_row, column=1, sticky=W, padx=5, pady=5)

        # 开始时间标签
        ttkb.Label(time_frame, text="开始时间:", font=(self.props.ui_font_family, 9)).grid(row=0, column=0, sticky=W, padx=(0, 5))

        # 开始时间 - 小时Spinbox
        start_hour_spinbox = ttkb.Spinbox(time_frame, from_=0, to=23, width=spinbox_width, format="%02.0f")
        start_hour_spinbox.grid(row=0, column=1, padx=2)

        ttkb.Label(time_frame, text=":", font=(self.props.ui_font_family, 10, "bold")).grid(row=0, column=2)

        # 开始时间 - 分钟Spinbox
        start_minute_spinbox = ttkb.Spinbox(time_frame, from_=0, to=59, width=spinbox_width, format="%02.0f")
        start_minute_spinbox.grid(row=0, column=3, padx=2)

        # 分隔符
        ttkb.Label(time_frame, text=" - ", font=(self.props.ui_font_family, 10, "bold")).grid(row=0, column=4, padx=5)

        # 结束时间标签
        ttkb.Label(time_frame, text="结束时间:", font=(self.props.ui_font_family, 9)).grid(row=0, column=5, sticky=W, padx=(0, 5))

        # 结束时间 - 小时Spinbox
        end_hour_spinbox = ttkb.Spinbox(time_frame, from_=0, to=23, width=spinbox_width, format="%02.0f")
        end_hour_spinbox.grid(row=0, column=6, padx=2)

        ttkb.Label(time_frame, text=":", font=(self.props.ui_font_family, 10, "bold")).grid(row=0, column=7)

        # 结束时间 - 分钟Spinbox
        end_minute_spinbox = ttkb.Spinbox(time_frame, from_=0, to=59, width=spinbox_width, format="%02.0f")
        end_minute_spinbox.grid(row=0, column=8, padx=2)

        # 解析现有配置值并设置到Spinbox中,不存在则使用默认值,其格式在使用前就进行校验,是一定可以解析的
        existing_value = self.props.server_config_values.get(cur_run_time)
        if existing_value:
            start_time_str, end_time_str = existing_value.split('-')
            start_hour, start_minute = start_time_str.split(':')
            end_hour, end_minute = end_time_str.split(':')
            start_hour_spinbox.set(start_hour)
            start_minute_spinbox.set(start_minute)
            end_hour_spinbox.set(end_hour)
            end_minute_spinbox.set(end_minute)
        # 格式校验提醒
        ttkb_gui_utils.comm_entry_validate(start_hour_spinbox, validate_func=constants.HOUR_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(start_minute_spinbox, validate_func=constants.MINUTE_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(end_hour_spinbox, validate_func=constants.HOUR_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(end_minute_spinbox, validate_func=constants.MINUTE_PATTERN.fullmatch)

        # 存储Spinbox控件的引用，用于后续获取值
        self.props.config_input_widgets[cur_run_time] = {
            'start_hour': start_hour_spinbox,
            'start_minute': start_minute_spinbox,
            'end_hour': end_hour_spinbox,
            'end_minute': end_minute_spinbox
        }

    def save_config_changes(self):
        """保存服务端配置项 --- 保存到配置文件和对应的变量中"""
        if self.props.server_process_alive():
            self.props.show_operation_error(error=None,error_msg= "服务端正在运行,请先停止服务端,再保存服务端配置项!",parent_gui=self.props.server_config_dialog)
            return
        cur_server_config_values = {}
        for config_key, entry_widget in self.props.config_input_widgets.items():
            if config_key == "run_time":
                # 处理run_time的Spinbox控件
                start_hour = entry_widget['start_hour'].get()
                start_minute = entry_widget['start_minute'].get()
                end_hour = entry_widget['end_hour'].get()
                end_minute = entry_widget['end_minute'].get()
                # 格式化为HH:MM-HH:MM格式
                run_time_value = f"{start_hour}:{start_minute}-{end_hour}:{end_minute}"
                cur_server_config_values[config_key] = run_time_value
            else:
                # 其他配置项使用普通Entry的get方法
                cur_server_config_values[config_key] = str(entry_widget.get())
        try:
            self.props.config_manager.update_config('server', cur_server_config_values)
            self.props.server_config_values.update(cur_server_config_values)
        except Exception as check_server_config_except:
            self.props.show_operation_error(error=check_server_config_except,error_msg= f"保存服务端配置失败\n {check_server_config_except}",parent_gui=self.props.server_config_dialog)
            return
        if messagebox.askyesno("成功","服务端配置项保存成功,是否退出服务端配置界面?",parent=self.props.server_config_dialog):
            gui_utils.gui_close(self.props.server_config_dialog)
