"""关于对话框组件。

负责显示软件的关于信息。
"""
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import ttkb_gui_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties


class AboutDialogComponent:
    """关于对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化关于对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props


    def open_about_dialog(self):
        """打开软件关于界面"""
        if getattr(self.props, "about_dialog", None) and self.props.about_dialog.winfo_exists():
            self.props.about_dialog.lift()
            return
        cur_gui_width = 550
        # 如果一个内容存在很长,这个就是其自动换行的行长度
        mult_content_line_additional_width = 150
        self.props.about_dialog = ttkb.Toplevel(master=self.props.root,title="关于",size=(cur_gui_width, 250),takefocus=True,topmost = True,resizable=(False, False),transient=self.props.root)
        ttkb_gui_utils.comm_child_win_do(self.props.about_dialog, self.props.root)
        # 主框架
        main_frame = ttkb.Frame(self.props.about_dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 标题
        title_label = ttkb.Label(main_frame,text=gui_constants.SOFTWARE_NAME,font=(self.props.ui_font_family, 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 使用网格布局创建整齐的键值对
        labels = [
            ("版本", gui_constants.VERSION),
            ("联系方式", gui_constants.CONTACT_INFORMATION),
            ("发布日期", gui_constants.RELEASE_TIME)
        ]

        # 创建标签和内容
        for row, (label_text, value_text) in enumerate(labels, start=1):
            # 标签列 (左对齐)
            ttkb.Label(main_frame, text=label_text + "：",
                       justify=RIGHT, anchor=E).grid(
                row=row, column=0, sticky=E, padx=(0, 5), pady=2)

            # 值列 (左对齐)
            ttkb.Label(main_frame, text=value_text,anchor=W).grid(row=row, column=1, sticky=W, pady=2)

        # 简介 (单独一行)
        row = len(labels) + 1
        ttkb.Label(main_frame, text="简要介绍：",anchor=E).grid(row=row, column=0, sticky=E, padx=(0, 5), pady=(10, 0))

        # 多行简介 (自动换行)
        ttkb.Label(main_frame,text=gui_constants.INSTRUCTION_SOFTWARE_USE,wraplength=cur_gui_width-mult_content_line_additional_width,justify=LEFT).grid(row=row, column=1, sticky=W, pady=(10, 0))

        # 许可协议 (单独一行)
        row += 1
        ttkb.Label(main_frame, text="许可协议：",anchor=E).grid(row=row, column=0, sticky=E, padx=(0, 5), pady=(10, 0))
        # 许可协议内容 自动换行
        license_agreement_content = gui_constants.LICENSE_AGREEMENT.replace(" ", "\u00A0")
        ttkb.Label(main_frame,text=license_agreement_content,wraplength=cur_gui_width-mult_content_line_additional_width,justify=LEFT).grid(row=row, column=1, sticky=W, pady=(10, 0))

        # 配置列权重
        main_frame.columnconfigure(0, weight=1, minsize=80)  # 标签列最小宽度
        main_frame.columnconfigure(1, weight=3)  # 内容列占3/4宽度

        # 绑定关闭事件
        self.props.about_dialog.protocol("WM_DELETE_WINDOW", self.props.about_dialog.destroy)
