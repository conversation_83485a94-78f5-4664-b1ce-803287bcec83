"""客户端信息对话框组件。

负责发信设备标识信息的管理。
"""
import copy
import functools
from tkinter import messagebox

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.models import gui_widgets
from common.utils import gui_utils, ttkb_gui_utils
from webhook_server.config import gui_constants, constants
from webhook_server.models import gui_properties
from webhook_server.utils import webhook_server_utils


class ClientInfoDialogComponent:
    """客户端信息对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化关于对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props

    def open_client_info_dialog(self):
        """打开客户端标识信息界面"""
        # 如果对话框已存在，则提到最前
        if getattr(self.props, "client_info_dialog", None) and self.props.client_info_dialog.winfo_exists():
            self.props.client_info_dialog.lift()
            return

        self.props.client_info_dialog = ttkb.Toplevel(master=self.props.root,title="发信设备标识管理",size=gui_constants.CLIENT_INFO_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.props.root)
        ttkb_gui_utils.comm_child_win_do(self.props.client_info_dialog, self.props.root)
        client_info_frame = ttkb.Frame(self.props.client_info_dialog)
        client_info_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 发信设备标识信息表格
        client_info_table_frame = ttkb.LabelFrame(client_info_frame, text="发信设备标识信息", bootstyle="card", padding=10) # noqa
        client_info_table_frame.pack(fill=X, pady=(0,10))
        client_info_table_frame.configure(height=200)
        client_info_table_frame.grid_propagate(False)
        client_info_table_frame.rowconfigure(0, weight=1)
        client_info_table_frame.columnconfigure(0, weight=1)
        column_dicts=[
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'key',gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '发信设备标识', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 200},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'description',gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '发信设备描述信息', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 320},
        ]
        self.props.client_info_table = gui_widgets.TreeviewWithFixedGrid(parent=client_info_table_frame, font_family=self.props.ui_font_family, font_size=11, columns= column_dicts, need_tree=False, selectmode="extended", bootstyle="info")

        scrollbar = ttkb.Scrollbar(client_info_table_frame ,orient="vertical", command=self.props.client_info_table.yview,bootstyle="round") # noqa
        self.props.client_info_table.configure(yscrollcommand=scrollbar.set)
        # 放置表格和滚动条
        self.props.client_info_table.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky=NS)

        # 添加双击事件绑定
        gui_utils.bind_double_click_handler(self.props.client_info_table, self.__edit_client)

        # 新增右键菜单功能
        self.props.client_right_click_menu = ttkb.Menu(self.props.client_info_table, tearoff=0)
        self.props.client_right_click_menu.add_command(label="编辑", command=self.__edit_client)
        # 绑定右键点击事件
        gui_utils.right_click_show_menu(self.props.client_info_table, self.props.client_right_click_menu)
        cur_client_info_data=[]
        if self.props.client_info_entries:
            for client_key, client_desc in self.props.client_info_entries.items():
                cur_client_info_data.append({'key':client_key,'description':client_desc})
            self.props.client_info_table.add_rows(cur_client_info_data)

        # 操作按钮区域
        btn_frame = ttkb.Frame(client_info_frame)
        btn_frame.pack(fill=X, pady=10)

        ttkb.Button(btn_frame, text="添加",bootstyle="secondary",  command=self.__add_client).pack(side=LEFT) # noqa
        ttkb.Button(btn_frame, text="编辑",bootstyle="secondary",  command=self.__edit_client).pack(side=LEFT, padx=5) # noqa
        ttkb.Button(btn_frame, text="删除",bootstyle="secondary",  command=self.__delete_client).pack(side=LEFT) # noqa


        ttkb.Button(btn_frame, text="保存修改",bootstyle="primary", command=self.save_client_config).pack(side=RIGHT) # noqa
        def on_close():
            # 检查是否有未保存的修改
            if self.props.file_client_info_entries != self.props.client_info_entries:
                if messagebox.askyesno("未保存的修改","有未保存的修改，是否放弃更改?",parent=self.props.client_info_dialog):
                    # 放弃更改并关闭窗口
                    self.client_info_entries=copy.deepcopy(self.props.file_client_info_entries)
                    gui_utils.gui_close(self.props.client_info_dialog)
                else:
                    # 用户选择继续编辑，不关闭窗口
                    return
            else:
                gui_utils.gui_close(self.props.client_info_dialog)

        # 绑定关闭事件
        self.props.client_info_dialog.protocol("WM_DELETE_WINDOW", on_close)

    def __add_client(self):
        # 添加发信设备标识对话框
        self.__show_add_client_dialog("添加发信设备信息")

    def __edit_client(self):
        selected = self.props.client_info_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个发信设备信息进行编辑",parent=self.props.client_info_dialog)
            return
        # 只能单选
        if len(selected) > 1:
            messagebox.showwarning("警告", "只能选择一个发信设备信息进行编辑",parent=self.props.client_info_dialog)
            return
        self.__show_add_client_dialog("编辑发信设备信息", selected[0])

    def __delete_client(self):
        # 选中节点id[可多选]
        selected = self.props.client_info_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请至少先选择一个发信设备信息进行删除",parent=self.props.client_info_dialog)
            return
        if selected:
            for item_id in selected:
                del self.props.client_info_entries[self.props.client_info_table.item(item_id, 'values')[0]]
                self.props.client_info_table.delete(item_id)
        self.props.client_info_table.update_separators()

    def __show_add_client_dialog(self, dialog_title: str, cur_selected_id: str = None):
        """显示添加或编辑单个发信设备标识信息对话框"""
        # 显示添加发信设备标识对话框
        cur_key = ""
        cur_desc = ""
        if cur_selected_id:
            cur_key, cur_desc = self.props.client_info_table.item(cur_selected_id, 'values')
        dialog = ttkb.Toplevel(master=self.props.client_info_dialog,title=dialog_title,size=gui_constants.CLIENT_NEW_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.props.client_info_dialog)
        ttkb_gui_utils.comm_child_win_do(dialog, self.props.client_info_dialog)
        dialog.columnconfigure(1, weight=1)
        ttkb.Label(dialog, text="发信设备标识:").grid(row=0, column=0, padx=10, pady=5, sticky=E)
        key_entry = ttkb.Entry(dialog, width=40)
        key_entry.insert(0, cur_key)
        key_entry.grid(row=0, column=1, padx=10, pady=5, sticky=EW)
        ttkb_gui_utils.comm_entry_validate(key_entry, validate_func=constants.CLIENT_KEY_PATTERN.fullmatch)

        ttkb.Label(dialog, text="发信设备描述:").grid(row=1, column=0, padx=10, pady=5, sticky=E)
        desc_entry = ttkb.Entry(dialog, width=40)
        desc_entry.insert(0, cur_desc)
        desc_entry.grid(row=1, column=1, padx=10, pady=5, sticky=EW)
        def check_desc_input(input_str: str)->bool:
            # 描述输入框必须大于1个字符
            return input_str and len(input_str)>1

        ttkb_gui_utils.comm_entry_validate(desc_entry, validate_func=check_desc_input)

        btn_frame = ttkb.Frame(dialog)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=15)

        save_funct=functools.partial(self.__save_client_info, dialog=dialog, key_entry=key_entry, desc_entry=desc_entry, cur_selected_id=cur_selected_id)
        ttkb.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=RIGHT)
        ttkb.Button(btn_frame, text="保存",command=save_funct).pack(side=RIGHT, padx=(0, 10))

        dialog.bind("<Return>", lambda e: self.__save_client_info(dialog, key_entry, desc_entry, cur_selected_id))
        dialog.bind("<Escape>", lambda e: dialog.destroy())
        # 绑定关闭事件
        dialog.protocol("WM_DELETE_WINDOW", dialog.destroy)

    def __save_client_info(self, dialog: ttkb.Toplevel, key_entry: ttkb.Entry, desc_entry: ttkb.Entry, cur_selected_id: str = None):
        """保存或更新单个发信设备标识信息 :[当cur_client_id值存在时,即修改] --- client_key不能重复"""
        # 默认选择节点id存在即client_tree中存在该节点
        client_key = key_entry.get()
        client_desc = desc_entry.get()
        if not client_key or not client_desc:
            messagebox.showerror("错误", "发信设备标识和描述不能为空",parent=dialog)
            return
        if not webhook_server_utils.check_one_client_info(client_key, client_desc):
            messagebox.showerror("错误", constants.ERROR_CLIENT_INPUT_MSG,parent=dialog)
            return
        # 新的设备标识不能与已有的重复
        old_client_key = None
        if cur_selected_id:
            old_client_key = self.props.client_info_table.item(cur_selected_id, 'values')[0]
        if client_key in self.props.client_info_entries and (cur_selected_id is None or old_client_key != client_key):
            messagebox.showerror("错误", "发信设备标识不能和已有的设备标识重复,请重新输入重试",parent=dialog)
            return
        if cur_selected_id:
            # 修改发信设备信息
            self.props.client_info_table.item(cur_selected_id, values=(client_key, client_desc))
            del self.props.client_info_entries[old_client_key]
        else:
            # 添加发信设备信息
            new_client_info_data=[{'key':client_key,'description':client_desc}]
            self.props.client_info_table.add_rows(new_client_info_data)
        self.props.client_info_entries[client_key] = client_desc
        dialog.destroy()

    def save_client_config(self):
        if self.props.server_process_alive():
            self.props.show_operation_error(error=None, error_msg= "服务端正在运行,请先停止服务端,再保存发信设备标识信息!",parent_gui=self.props.client_info_dialog)
            #回退修改操作
            self.props.client_info_entries=copy.deepcopy(self.props.file_client_info_entries)
            return
        try:
            self.props.config_manager.update_config("client_info", self.props.client_info_entries)
            self.props.file_client_info_entries=copy.deepcopy(self.props.client_info_entries)
        except Exception as check_client_config_except:
            self.props.show_operation_error(error=check_client_config_except,error_msg= str(check_client_config_except),parent_gui=self.props.client_info_dialog)
            return
        if messagebox.askyesno("成功","发信设备标识信息保存成功，是否退出发信设备标识信息界面?",parent=self.props.client_info_dialog):
            gui_utils.gui_close(self.props.client_info_dialog)
