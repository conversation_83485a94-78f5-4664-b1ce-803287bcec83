"""Webhook服务器GUI主入口文件。

此文件提供了与原webhook_server_gui.py相同的接口，但使用了重构后的模块化设计。
"""

import argparse
import sys
from pathlib import Path

# 确保源代码路径在sys.path中
src_path = str(Path(__file__).parent.parent.parent)
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from webhook_server.gui.main_window import WebhookServerGUI


def main():
    """主函数。"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    config_path = parser.parse_args().config
    main_app = WebhookServerGUI(config_path)
    main_app.props.root.mainloop()


if __name__ == '__main__':
    main()
