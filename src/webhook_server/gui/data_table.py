"""数据表格组件。

负责显示实时数据的表格组件。
"""
from typing import List

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.models import gui_widgets
from common.utils import gui_utils, ttkb_gui_utils, logging_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties, server_data, gui_server_info


class DataTableComponent:
    """数据表格组件类。"""

    def __init__(self, props: gui_properties.GUIProperties,content_frame:ttkb.Frame):
        """初始化数据表格组件。

        Args:
            props: GUI属性
            content_frame: 主界面中主要内容显示区域
        """
        self.props = props
        self.content_frame = content_frame

    def create_widgets(self):
        """创建数据表格组件"""
        # 数据表格区域（固定高度适合显示10行数据）
        data_frame = ttkb.LabelFrame(self.content_frame, text="实时最新10条数据")
        # 计算适合10行数据的高度：表头(30px) + 10行×25px + padding(40px)
        table_height = 30 + (10 * 25) + 40  # 320px
        data_frame.configure(height=table_height)
        data_frame.pack(fill=X, padx=10, pady=(0, 10))
        data_frame.pack_propagate(False)  # 防止子组件改变父容器大小

        # 创建表格 gui_constants.RED_COLOR
        column_dicts=[
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'message', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '数据内容[双击-复制完整数据内容]', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 290},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'reception_time', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '接收时间', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 175},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'client_key', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '设备标识', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 140},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'is_read', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '是否被其他设备读取', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 160},
        ]
        self.props.message_data_table = gui_widgets.TreeviewWithFixedGrid(parent=data_frame, font_family=self.props.ui_font_family, font_size=10, columns= column_dicts, need_tree=False, selectmode="browse")
        self.props.message_data_table.tag_configure(gui_properties.GUIProperties.NO_DATA_SHOW_TAG, foreground=gui_utils.rgb_to_hex((95, 4, 4)))
        # 表格填充容器
        self.props.message_data_table.pack(fill=BOTH, expand=True, padx=10, pady=1)

        # 绑定双击事件用于复制功能:实时数据表格中行的双击事件
        gui_utils.bind_double_click_handler(self.props.message_data_table, self.on_real_time_data_table_double_click)

    def on_real_time_data_table_double_click(self):
        """处理实时数据表格双击事件，实现数据内容复制功能"""
        if self.props.no_data_show:
            self.show_no_copied_popup()
            return
        self.props.logger.debug("webhook real-time data table double click event triggered")
        # 获取双击位置
        selected=self.props.message_data_table.selection()
        if not selected or len(selected) > 1 or not self.props.message_data_table.exists(selected[0]):
            return
        try:
            item_id = selected[0]
            # 选择该行
            self.props.message_data_table.selection_set(item_id)
            # 获取完整的message内容
            message_content = self.props.full_message_map[item_id]

            # 复制到剪贴板
            if message_content:
                self.copy_to_clipboard(message_content)
                self.props.logger.debug(f"copied message content to clipboard via double click, content length: {len(message_content)}")
                # 显示复制成功提示窗口
                self.show_copy_success_popup(message_content)
        except Exception:  # noqa
            self.props.logger.exception("error during webhook real-time data table double click handling")

    def show_no_copied_popup(self):
        """显示无法复制的提示窗口弹窗，3秒后自动消失"""
        try:
            # 显示提示消息
            content='当前服务端未运行,无数据可复制'

            # 创建提示窗口，完全不设置大小限制
            popup = ttkb.Toplevel(master=self.props.root,title="未运行不可复制",takefocus=True,topmost = True,transient=self.props.root)
            ttkb_gui_utils.comm_child_win_do(popup, self.props.root)

            # 创建提示内容，使用最简单的布局
            ttkb.Label(popup, text=content, font=(self.props.ui_font_family, 12, "bold")).pack(padx=20, pady=(20, 10))

            # 3秒后自动关闭窗口
            popup.after(3000, popup.destroy) # noqa
        except Exception:  # noqa
            self.props.logger.exception("error during showing no copied popup")


    def copy_to_clipboard(self, text):
        """将文本复制到剪贴板"""
        try:
            self.props.root.clipboard_clear()
            self.props.root.clipboard_append(text)
            self.props.root.update()  # 确保剪贴板更新
            self.props.logger.debug("webhook data message copied to clipboard successfully!")
        except Exception:  # noqa
            self.props.logger.exception("error during copying webhook data message to clipboard")

    def show_copy_success_popup(self, message_content):
        """显示复制成功的提示窗口，3秒后自动消失"""
        try:
            self.props.logger.debug("showing webhook data message copy success popup")

            # 显示复制的内容（截取前xx个字符用于预览）
            show_char_length = gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH
            content_preview = message_content[:show_char_length] + "..." if len(message_content) > show_char_length else message_content

            # 创建提示窗口，完全不设置大小限制
            popup = ttkb.Toplevel(master=self.props.root,title="系统剪贴板已复制完整内容成功",takefocus=True,topmost = True,transient=self.props.root)
            ttkb_gui_utils.comm_child_win_do(popup, self.props.root)

            # 创建提示内容，使用最简单的布局
            ttkb.Label(popup, text="数据内容完整复制成功！", font=(self.props.ui_font_family, 12, "bold")).pack(padx=20, pady=(20, 10))
            ttkb.Label(popup, text=f"数据前{show_char_length}个字符预览：{content_preview}", font=(self.props.ui_font_family, 9)).pack(padx=20, pady=(0, 20))

            # 3秒后自动关闭窗口
            popup.after(1200, popup.destroy) # noqa
            self.props.logger.debug("copy success popup displayed, will auto-close in 1 second")
        except Exception:  # noqa
            self.props.logger.exception("error during showing copy success popup")


    def clear_data(self):
        """服务端停止时不显示数据 --- 旧[stop_no_data_show] """
        # 清除数据显示
        logging_utils.logger_print(msg="clearing data", custom_logger=self.props.logger)
        # 清除现有数据（包括空数据提示）
        if hasattr(self.props, 'message_data_table'):
            self.props.message_data_table.delete(*self.props.message_data_table.get_children())

    def data_ui_no_show(self,show_msg:str):
        """在数据显示区域中不需要显示数据时,根据场景不同显示不同的空数据提示信息 --- 旧 _no_data_show"""
        self.props.message_data_table.clear_all_data()
        # 清除完整message映射
        self.props.full_message_map.clear()
        # 如果没有数据，在表格中插入居中提示信息
        self.props.logger.info("no data available, showing centered empty message spanning all columns")
        self.props.message_data_table.insert("", "end", values=('',show_msg,),tags=(gui_properties.GUIProperties.NO_DATA_SHOW_TAG,))
        self.props.no_data_show=True

    def refresh_ui(self, current_gui_server_info:gui_server_info.GUIServerInfo):
        """只有在服务端启动成功且后台刷新线程启动成功时才会刷新数据"""
        if current_gui_server_info.cur_get_new_data:
            self.refresh_data(current_gui_server_info.get_data())

    def refresh_data(self,new_data:List[server_data.MessageToGUI]):
        """刷新表格数据"""
        self.props.logger.info("refreshing data in real-time data table")
        try:
            # 清除现有数据
            self.clear_data()
            # 清除完整message映射
            self.props.full_message_map.clear()
            # 获取新数据
            self.props.logger.debug("fetching recent data from data manager")
            self.props.logger.debug(f"fetched {len(new_data)} data items")
            if not new_data:
                self.data_ui_no_show(show_msg="当前没有数据发送到本服务端")
            else:
                self.props.no_data_show=False
                # 有数据时插入实际数据
                self.props.logger.debug("data available, inserting data into tree")
                # 插入新数据
                new_message_data=[]
                full_messages = []  # 保存完整message内容
                for i, item in enumerate(new_data):
                    # 是否被其他设备读取
                    auth_status = "未被读取" if item.is_read == 0 else "已被其他设备读取数据"
                    # 处理message长度，超过 SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH 个字符则截断为 SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH 个字符+...
                    display_message = item.message
                    if len(display_message) > gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH:
                        display_message = display_message[:gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH] + "..."
                    self.props.logger.debug(f"inserting item {i+1}: message_length={len(item.message)}, client_key={item.client_key}, auth_status={auth_status}")
                    new_message_data.append({'message': display_message,'reception_time': item.reception_time, 'client_key': item.client_key, 'is_read': auth_status})
                    full_messages.append(item.message)  # 保存完整内容

                # 定义行处理器来保存完整message内容
                def message_row_processor(treeview, item_id, row_data, index, tag):  # noqa
                    # 保存完整message内容到映射中
                    self.props.full_message_map[item_id] = full_messages[index]

                self.props.message_data_table.add_rows(new_message_data, message_row_processor)
            self.props.logger.info("data refresh completed successfully")
        except Exception:  # noqa
            self.props.logger.exception("error during data refresh")
            raise
