"""
在 webhook server gui 主界面中 悬停在"程序使用注意事项"显示的提示信息：
        1. 用户使用注意事项：
            1.1 在服务端运行期间不能修改设置中的配置项
            1.2 发信设备网络必须和运行当前程序的机器的网络在同一个局域网中
"""
import re

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.models import gui_widgets
from common.utils import network_utils
from webhook_server.models import gui_properties


class UsageNotesComponent:
    """程序使用注意事项组件类。"""

    def __init__(self, props: gui_properties.GUIProperties,content_frame:ttkb.Frame):
        """初始化数据表格组件。

        Args:
            props: GUI属性
            content_frame: 主界面中主要内容显示区域 --- 当前组件的父容器
        """
        self.props = props
        self.content_frame = content_frame


    def create_widgets(self):
        """在主界面中创建程序使用注意事项组件"""
        # 注意事项区域
        notice_frame = ttkb.Frame(self.content_frame)
        notice_frame.pack(pady=(0, 10), fill=X)
        notice_frame.grid_columnconfigure(0, weight=1)  # 左半边，空着
        notice_frame.grid_columnconfigure(1, weight=0)  # 右边，放label
        label_container = ttkb.Frame(notice_frame)
        label_container.grid(row=0, column=1, sticky="e")
        # 鼠标悬浮显示提示信息
        hover_label = gui_widgets.TooltipLabel(label_container, self._build_notice_hover_frame, position='right', text='▲程序使用注意事项▼', offset_x=-720, offset_y=-320, bootstyle="inverse-info")
        hover_label.grid(row=0, column=1, sticky="ew", padx=(0, 10))

    def _build_notice_hover_frame(self,hover_label_frame:ttkb.Frame):
        """
        创建服务端注意事项提示信息区域
        :param hover_label_frame:  是TooltipLabel中popup的frame
        """
        # 设置悬浮框背景色和边框
        hover_label_frame.configure(relief="solid", borderwidth=1)

        # 标题
        title_label = ttkb.Label(hover_label_frame, text="📋 程序使用注意事项",font=(self.props.ui_font_family, 11, "bold"),bootstyle="info") # noqa
        title_label.pack(anchor=W, padx=8, pady=(8, 5))

        # 分隔线
        separator = ttkb.Separator(hover_label_frame, orient="horizontal")
        separator.pack(fill=X, padx=8, pady=2)

        # 注意事项内容
        notices = [
            "1. 服务端配置界面配置运行时段:",
            "   • 必须符合时间格式(HH:MM:SS)且在00:00-23:59范围内",
            "   • 开始时间和结束时间一致表示服务端支持全天运行",
            "   • 开始时间小于结束时间表示常规情况下服务端从开始时间运行到结束时间",
            "   • 开始时间大于结束时间表示支持跨天运行,服务端从今天的开始时间运行到明天的结束时间,如05:00-04:00就表示从今天的5点运行到明天的4点",
            "",
            "2. 服务端运行期间:",
            "   • 不能修改设置中的配置项",
            "   • 双击实时数据表格中的指定行可复制完整数据内容",
            "",
            "3. 发信设备的网络必须和本程序运行的机器的网络处于同一局域网",
            "",
            "4. 发信设备请求链接信息:",
            f"   • Header: X-Client-Key=发信设备标识信息界面中设备标识值",
            f"   • URL: http://{network_utils.get_local_lan_ip()}:{self.props.server_config_values['port']}/webhook/save", # noqa
            "",
            "5. 发信内容要求:",
            "   • 字符尽量不要使用特殊字符,可能无法正常显示",
        ]
        # 正则表达式匹配行标题
        title_pattern = re.compile(r"^\d+\.\s")
        for notice in notices:
            if notice == "":  # 空行
                ttkb.Label(hover_label_frame, text="").pack(anchor=W, padx=8, pady=1)
            elif notice.startswith("   •"):  # 子项目
                ttkb.Label(hover_label_frame, text=notice,
                           font=(self.props.ui_font_family, 9),
                           foreground="#666666").pack(anchor=W, padx=8, pady=1)
            elif title_pattern.match(notice):  # 主项目
                ttkb.Label(hover_label_frame, text=notice,
                           font=(self.props.ui_font_family, 10, "bold"),
                           bootstyle="warning").pack(anchor=W, padx=8, pady=(3, 1)) # noqa
            else:  # 其他文本
                ttkb.Label(hover_label_frame, text=notice,
                           font=(self.props.ui_font_family, 9),
                           foreground="#333333").pack(anchor=W, padx=8, pady=1)

        # 底部间距
        ttkb.Label(hover_label_frame, text="").pack(pady=5)
