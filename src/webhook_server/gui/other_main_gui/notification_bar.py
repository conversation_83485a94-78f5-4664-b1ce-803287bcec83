"""
主界面顶部滚动条通知栏
"""
from common.utils import ttkb_gui_utils
from webhook_server.models import gui_properties


class NotificationBarComponent:
    def __init__(self, props: gui_properties.GUIProperties):
        """初始化关于对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props

    def create_widgets(self):
        """创建通知栏组件。"""
        ttkb_gui_utils.show_notification(win=self.props.root, text=self.props.notification_bar_text, font_family_size=(self.props.ui_font_family, 12))
