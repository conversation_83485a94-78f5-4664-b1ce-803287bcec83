"""GUI属性管理类。

统一管理Webhook服务器GUI的所有属性、常量和配置项。
"""
import logging
import multiprocessing
import subprocess
import sys
import threading
import tkinter
from typing import Dict, Optional, Union, Any
from zoneinfo import ZoneInfo

import ttkbootstrap as ttkb

from common.constants import const
from common.models import gui_widgets
from common.utils import process_monitor, logging_utils, gui_utils
from webhook_server.config import gui_constants, constants
from webhook_server.models import gui_server_info, server_data_manager
from webhook_server.utils import config_lock, webhook_server_utils


class GUIProperties:
    """GUI属性管理类。
    
    统一管理所有GUI相关的属性、常量和配置项，避免属性分散在各个组件中。
    """
    
    # 类常量
    NO_DATA_SHOW_TAG = 'no_data_show'  # 在没有数据时实时数据表格中行的标签
    
    def __init__(self, server_config_path: str):
        self.logger: Optional[logging.Logger] = None
        """初始化GUI属性。"""
        logging_utils.logger_print(msg="initializing webhook server gui", custom_logger=self.logger)
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING, zone=const.DEFAULT_TIMEZONE)
        # 服务端配置文件路径
        logging_utils.logger_print(msg="setting up server config path", custom_logger=self.logger)
        self.validate_config_file_path(server_config_path)
        self.config_file_path = server_config_path
        self.config_file_time_zone:Optional[ZoneInfo] = None
        logging_utils.logger_print(msg=f"using provided config path: {server_config_path}", custom_logger=self.logger)

        # 服务端状态信息
        # 服务端总运行时间
        self.total_runtime_seconds = 0
        # 服务端点击启动按钮的时间 而非第一次初始化启动的时间 time.monotonic()
        self.server_start_time:Optional[float] = None
        self.status_label: Optional[ttkb.Label] = None
        # 显示第一次启动的时间
        self.server_initial_startup_time:Optional[ttkb.Label] = None

        self.cpu_meter: Optional[gui_widgets.PrecisionMeter] = None
        self.start_btn: Optional[ttkb.Button] = None
        self.stop_btn: Optional[ttkb.Button] = None
        self.message_data_table: Optional[gui_widgets.TreeviewWithFixedGrid] = None # 实时数据表格 gui组件
        # 保存完整message内容的映射,用于双击复制完整内容 --- item_id:message
        self.full_message_map: Dict[str, str] = {}
        # gui界面中按钮手动停止后台更新服务端状态信息线程
        self.stop_server_event = threading.Event()
        self.gui_server_info:Optional[gui_server_info.GUIServerInfo] = None
        self.server_refresh_thread: Optional[threading.Thread] = None # 后台更新服务端状态信息线程
        self.had_initialized_startup=False

        # 性能优化：减少不必要的UI更新
        self.last_ui_update = 0
        self.ui_update_interval = 1.0  # 1秒更新一次UI

        # 该值是使用配置文件加载到该变量上,其键值对和实际配置文件是完全一致的,方便直接使用,所有的变量的值都是字符串类型
        self.server_config_values:dict[str,Any] = {}
        # 该变量和client_tree一起使用
        self.client_info_entries = {} #实时客户端设备标识界面信息
        self.file_client_info_entries={} # 实际配置文件中客户端设备标识信息
        self.log_config_path:Optional[str] = None
        self.error_occured = False
        self.message_data_manager: Optional[server_data_manager.WebhookDataManager] = None
        self.no_data_show=True
        self.notification_bar_text:str = gui_constants.DEFAULT_NOTIFICATION_MESSAGE

        # gui元素变量
        self.server_config_dialog: Optional[ttkb.Toplevel] = None
        self.client_info_dialog: Optional[ttkb.Toplevel] = None
        self.about_dialog: Optional[ttkb.Toplevel] = None
        self.client_info_table: Optional[gui_widgets.TreeviewWithFixedGrid] = None # 用户自定义客户端标识信息的gui表格
        self.client_right_click_menu: Optional[ttkb.Menu] = None  # 客户端标识界面表格中右键菜单
        self.config_input_widgets: Dict[str, Union[tkinter.Entry,dict[str,tkinter.Entry]]] = {}  # 用户自定义服务端配置项的Entry组件映射,可能一个项对应多个Entry组件
        # gui字体
        self.ui_font_family:Optional[str]=None
        self.server_total_uptime_var:Optional[ttkb.StringVar]=None
        self.server_pid_var:Optional[ttkb.StringVar]=None
        self.server_memory_var:Optional[ttkb.StringVar]=None
        # 服务端进程
        self.server_process:Optional[subprocess.Popen]=None
        self.stop_server_process_event:Optional[multiprocessing.Event]=None

        # 服务端进程监控器，专门监控server_process
        self.server_monitor:Optional[process_monitor.ProcessMonitor]=None
        # 主窗口
        self.root:Optional[ttkb.Window]=None

    def validate_config_file_path(self,server_config_path:str):
        """在初始化时,校验配置文件路径所对应的配置文件是否符合要求"""
        try:
            self.config_manager.main_gui_load_config(server_config_path)
        except Exception as e:
            webhook_server_utils.write_error_to_temp_file(str(e))
            sys.exit(1)

    def server_process_alive(self)->bool:
        """判断服务端进程是否存活"""
        return hasattr(self, "server_process") and self.server_process is not None and self.server_process.poll() is None

    def show_operation_error(self, error, error_msg, parent_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]], no_exit=True):
        """集中处理用户操作错误"""
        self.error_occured = True
        gui_utils.handle_code_error(error=error, error_msg=error_msg, parent_gui=parent_gui, no_exit=no_exit)
