--config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini

D:\Git\python-samples-hub\src\webhook_server\webhook_server_gui.py --config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini 

D:\Git\python-samples-hub\src\webhook_server\config_selection_gui.py 

D:\Python\Python311\python.exe D:\Git\python-samples-hub\src\webhook_server\webhook_server_gui.py --config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini 

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)


生成exe --- 构建测试
分析本项目，给出建议哪些代码文件需要重构，仅仅是给出建议，不需要修改代码
分析本项目，给出建议，哪些代码文件需要重命名，使其更加符合代码文件内容实际意义，仅仅是给出建议，不需要修改代码
分析本项目，给出建议，哪些代码方法需要重命名，使其更加符合代码方法内实现逻辑，仅仅是给出建议，不需要修改代码

webhook_server_gui代码文件重构，将该类的代码重构到webhook_server/gui目录下，只保存main部分实现逻辑
webhook_server_gui 主界面在设置下新增一个"软件设置"项,点击该项弹出软件设置窗口，该窗口目前就一个配置项，点击主界面右上角x是关闭程序还是最小化到任务栏 --- 默认是最小化到任务栏 这个选项是一个 ttkbootstrap 中的 Checkbutton(bootstyle="success-round-toggle")
该配置项保持到 gui_constants.SOFTWARE_CONFIG_PATH 所对应的文件中，如果该ini配置文件不存在就进行创建，该配置项在程序界面初始化时就会读取并使用，默认值 true 程序最小化
在该配置项为 点击右上角x效果是最小化时，就会将程序最小化到任务栏中，如果该配置项点击右上角x效果是关闭，则按照原本关闭逻辑执行
当程序缩小到任务栏中，双击可以打开主界面；右键显示两个选项：1 打开主界面 2. 退出程序

注意：
该功能只在webhook_server_gui 主界面中生效，而 config_selection_gui 界面不受影响


doc目录下文档更新说明，其中内容需要根据最新当前项目代码进行更新，并指明其是用于webhook_server项目


将 专业文件中cron中的read.py --- 分析获取短信验证码 功能实现到common中的utils中
删除 git\1 目录

定时任务脚本中需要补充，只有存在时才执行
cron目录统一完成之后，补充什么时候执行定时任务 --- 补充定时任务机制

python downloadBili.py --req-cookie ../conf/bili_cookies.txt --ytdlp-cookie ../conf/bili_Netscape.txt --media-id 3179004075
python downloadBili.py --file urls.txt --out ./downloads --workers 4